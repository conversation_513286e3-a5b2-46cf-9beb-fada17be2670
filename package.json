{"name": "st-louis-demo-school", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "clean": "node scripts/clean-dependencies.js", "fresh-install": "npm run clean && npm install"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/material": "^5.14.18", "currency-symbol-map": "^5.1.0", "framer-motion": "^10.16.4", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-iframe": "^1.8.5", "react-modal-image": "^2.6.0", "react-router-dom": "^6.18.0", "yet-another-react-lightbox": "^3.23.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "axios": "^1.9.0", "cheerio": "^1.0.0", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}