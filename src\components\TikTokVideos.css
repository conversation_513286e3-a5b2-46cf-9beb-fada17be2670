/* TikTok Videos Component Styles */

.tiktok-videos-section {
  padding: 2rem;
  background: linear-gradient(135deg, #000 0%, #1a1a1a 100%);
  color: white;
  min-height: 400px;
}

.tiktok-videos-section h2 {
  text-align: center;
  color: #ff0050;
  font-size: 2.5rem;
  margin-bottom: 2rem;
  text-shadow: 0 0 10px rgba(255, 0, 80, 0.3);
}

.videos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.video-card {
  background: #111;
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid #333;
  transition: all 0.3s ease;
  overflow: hidden;
}

.video-card:hover {
  border-color: #ff0050;
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(255, 0, 80, 0.2);
}

.tiktok-embed-container {
  position: relative;
  margin-bottom: 1rem;
}

/* TikTok Embed Styles */
.tiktok-embed {
  margin: 0 auto;
  max-width: 100%;
  min-height: 400px;
}

/* Fallback Video Preview */
.tiktok-fallback {
  width: 100%;
}

.video-preview {
  background: #222;
  border-radius: 12px;
  padding: 1.5rem;
  border: 2px dashed #444;
  text-align: center;
  transition: all 0.3s ease;
}

.video-preview:hover {
  border-color: #ff0050;
  background: #2a2a2a;
}

.video-preview h3 {
  color: #ff0050;
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
}

.video-info {
  text-align: center;
}

.username {
  font-weight: bold;
  color: #fff;
  margin: 0.5rem 0;
  font-size: 1.1rem;
}

.description {
  font-style: italic;
  color: #ccc;
  margin: 1rem 0;
  line-height: 1.4;
  max-height: 3.6em;
  overflow: hidden;
  text-overflow: ellipsis;
}

.video-stats {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin: 1rem 0;
  font-size: 0.9rem;
  color: #888;
}

.watch-button {
  display: inline-block;
  background: linear-gradient(45deg, #ff0050, #ff1a5c);
  color: white;
  padding: 12px 24px;
  border-radius: 25px;
  text-decoration: none;
  margin: 1rem 0;
  transition: all 0.3s ease;
  font-weight: bold;
  box-shadow: 0 4px 15px rgba(255, 0, 80, 0.3);
}

.watch-button:hover {
  background: linear-gradient(45deg, #ff1a5c, #ff3366);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 0, 80, 0.4);
  color: white;
  text-decoration: none;
}

.help-text {
  font-size: 0.8rem;
  color: #666;
  margin-top: 0.5rem;
}

.video-metadata {
  border-top: 1px solid #333;
  padding-top: 1rem;
  margin-top: 1rem;
}

.video-user {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  color: #888;
}

/* Loading States */
.tiktok-loading {
  text-align: center;
  padding: 3rem;
  color: #ccc;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #333;
  border-top: 3px solid #ff0050;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error States */
.tiktok-error {
  text-align: center;
  padding: 3rem;
  color: #ff6b6b;
  background: #2a1a1a;
  border-radius: 10px;
  margin: 2rem;
}

.retry-button {
  background: #ff0050;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  margin-top: 1rem;
  transition: background 0.3s ease;
}

.retry-button:hover {
  background: #ff1a5c;
}

/* Empty State */
.tiktok-empty {
  text-align: center;
  padding: 3rem;
  color: #ccc;
  background: #1a1a1a;
  border-radius: 10px;
  margin: 2rem;
}

.tiktok-empty h3 {
  color: #ff0050;
  margin-bottom: 1rem;
}

/* Footer */
.videos-footer {
  text-align: center;
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid #333;
  color: #666;
}

.last-update {
  font-size: 0.8rem;
  margin-top: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .tiktok-videos-section {
    padding: 1rem;
  }
  
  .tiktok-videos-section h2 {
    font-size: 2rem;
  }
  
  .videos-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .video-card {
    padding: 1rem;
  }
  
  .video-stats {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .video-user {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
}

/* Dark theme compatibility */
@media (prefers-color-scheme: dark) {
  .tiktok-videos-section {
    background: linear-gradient(135deg, #000 0%, #1a1a1a 100%);
  }
}
