// Type declarations for Lucide React icons

declare module 'lucide-react/dist/esm/icons/loader' {
  import { LucideProps } from 'lucide-react';
  const Loader: React.FC<LucideProps>;
  export default Loader;
}

declare module 'lucide-react/dist/esm/icons/banknote' {
  import { LucideProps } from 'lucide-react';
  const Banknote: React.FC<LucideProps>;
  export default Banknote;
}

declare module 'lucide-react/dist/esm/icons/bank' {
  import { LucideProps } from 'lucide-react';
  const Bank: React.FC<LucideProps>;
  export default Bank;
}
